<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layered Background System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            overflow: hidden;
        }

        .background-container {
            
            position: relative;
            width: 1440px;
            height: 900px;
            overflow: hidden;
            transition: all 0.3s ease-in-out;
        }

        /* Light Mode Background */
        [data-theme="light"] .background-container {
            background: #ffffff;

        }

        /* Dark Mode Background */
        [data-theme="dark"] .background-container {
            background: #1e1e1e;
        }

        /* Blob Container */
        .blobs-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* Base Blob Styles */
        .blob {
            position: absolute;
            filter: blur(250px);
            transition: all 0.3s ease-in-out;
        }

        /* Light Mode Blobs */
        [data-theme="light"] .blob-1 {
            background: #bbbec5;
            width: 585px;
            height: 381px;
            left: 482px;
            top: 517px;
            border-radius: 585px;
        }

        [data-theme="light"] .blob-2 {
            background: #2563EB;
            width: 477px;
            height: 381px;
            left: 1025px;
            top: 0px;
            border-radius: 477px;
        }

        [data-theme="light"] .blob-3 {
            background: #8561C5;
            width: 477px;
            height: 381px;
            left: 0px;
            top: 191px;
            border-radius: 477px;
        }

        /* Dark Mode Blobs */
        [data-theme="dark"] .blob-1 {
            background: #673AB7;
            width: 477px;
            height: 381px;
            left: 0px;
            top: 191px;
            border-radius: 477px;
        }

        [data-theme="dark"] .blob-2 {
            background: #341864;
            width: 477px;
            height: 381px;
            left: 1025px;
            top: 0px;
            border-radius: 477px;
        }

        [data-theme="dark"] .blob-3 {
            background: #00376B;
            width: 585px;
            height: 381px;
            left: 482px;
            top: 517px;
            border-radius: 585px;
        }

        /* Overlay Layer */
        .overlay-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            transition: all 0.3s ease-in-out;
        }

        /* Light Mode Overlay */
        [data-theme="light"] .overlay-layer {
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.30) 100%);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* Dark Mode Overlay */
        [data-theme="dark"] .overlay-layer {
            background: rgba(30, 30, 30, 0.10);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #333;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        [data-theme="dark"] .theme-toggle {
            color: #fff;
            background: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .theme-toggle:hover {
            background: rgba(0, 0, 0, 0.3);
        }


    </style>
</head>
<body>
    <div class="background-container">
        <!-- Blobs Layer -->
        <div class="blobs-layer">
            <div class="blob blob-1"></div>
            <div class="blob blob-2"></div>
            <div class="blob blob-3"></div>
        </div>
        <div class="overlay-layer"></div>

        <!-- Theme Toggle -->
        <button class="theme-toggle" id="themeToggle">🌙 Dark Mode</button>
    </div>

    <script>
        let currentTheme = 'light';
        const container = document.querySelector('.background-container');
        const themeToggle = document.getElementById('themeToggle');

        function updateTheme() {
            container.setAttribute('data-theme', currentTheme);
            themeToggle.textContent = currentTheme === 'light' ? '🌙 Dark Mode' : '☀️ Light Mode';
        }

        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            updateTheme();
        }

        themeToggle.addEventListener('click', toggleTheme);

        // Keyboard shortcut (T key)
        document.addEventListener('keydown', (e) => {
            if (e.key === 't' || e.key === 'T') {
                toggleTheme();
            }
        });
    </script>
</body>
</html>
